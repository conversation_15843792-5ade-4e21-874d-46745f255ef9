language: cpp
sudo: false   # Use the new container infrastructure

matrix:
  include:
    - os: linux
      env:
        - COMPILER=g++-7
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test']
          packages: ["g++-7", "cmake-data", "cmake"]
    - os: linux
      env:
        - COMPILER=g++-8
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test']
          packages: ["g++-8", "cmake-data", "cmake"]
    - os: linux
      env:
        - COMPILER=g++-9
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test']
          packages: ["g++-9", "cmake-data", "cmake"]
    - os: linux
      env:
        - COMPILER=g++-9 USE_BOOST_REGEX=ON
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test']
          packages: ["g++-9", "cmake-data", "cmake", "libboost-regex-dev"]

    - os: linux
      env:
        - COMPILER=clang++-3.6 STDLIB=libc++
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test', 'llvm-toolchain-precise-3.6', 'george-edison55-precise-backports']
          packages: ["clang-3.6", "cmake-data", "cmake"]

    - os: linux
      env:
        - COMPILER=clang++-8 STDLIB=libc++
      addons:
        apt:
          sources: ['ubuntu-toolchain-r-test', 'llvm-toolchain-trusty-8']
          packages: ["clang-8", "cmake-data", "cmake"]

    - os: osx
      osx_image: xcode9.4
      env:
        - COMPILER=clang++ V='Apple LLVM 9.1'
        - COMPILER=clang++ V='Apple LLVM 9.1' WITH_CPP14=true

    - os: osx
      osx_image: xcode10.3
      env:
        - COMPILER=clang++ V='Apple LLVM 10.0'
        - COMPILER=clang++ V='Apple LLVM 10.0' WITH_CPP14=true
    - os: osx
      osx_image: xcode11.2
      env:
        - COMPILER=clang++ V='Apple LLVM 11.0'
        - COMPILER=clang++ V='Apple LLVM 11.0' WITH_CPP14=true
    - os: osx
      osx_image: xcode11.2
      env:
        - COMPILER=clang++ V='Apple LLVM 11.0'
        - COMPILER=clang++ V='Apple LLVM 11.0' WITH_CPP17=true

before_install:
  - CMAKE_CXX_FLAGS+=" -Wall"
  - |
    if [[ "${WITH_CPP14}" == "true" ]]; then
        CMAKE_OPTIONS+=" -DCMAKE_CXX_STANDARD=14"
    fi
  - |
    if [[ "${WITH_CPP17}" == "true" ]]; then
        CMAKE_OPTIONS+=" -DCMAKE_CXX_STANDARD=17"
    fi
  - |
    if [[ "${USE_BOOST_REGEX}" == "ON" ]]; then
        CMAKE_OPTIONS+=" -DUSE_BOOST_REGEX=ON"
        CMAKE_OPTIONS+=" -DBoost_REGEX_LIBRARY_DEBUG=/usr/lib/x86_64-linux-gnu/libboost_regex.so"
        CMAKE_OPTIONS+=" -DBoost_REGEX_LIBRARY_RELEASE=/usr/lib/x86_64-linux-gnu/libboost_regex.so"
    fi
  - |
    if [[ "${STDLIB}" == "libc++" ]]; then
        CMAKE_CXX_FLAGS+=" -stdlib=libc++"
    fi
  - ${COMPILER} --version

before_script:
  - rm -rf build/
  - mkdir build
  - cd build
  - cmake -DCMAKE_CXX_COMPILER=${COMPILER} -DCMAKE_CXX_FLAGS=${CMAKE_CXX_FLAGS} -DWITH_TESTS=1 -DWITH_EXAMPLE=1 ${CMAKE_OPTIONS} ..

script:
  - cmake --build .
  - python run_tests
