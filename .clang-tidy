Checks: '*,
         -altera-id-dependent-backward-branch,
         -altera-struct-pack-align,
         -altera-unroll-loops,
         -bugprone-easily-swappable-parameters,
         -bugprone-macro-parentheses,
         -bugprone-suspicious-include,
         -cppcoreguidelines-avoid-magic-numbers,
         -cppcoreguidelines-avoid-non-const-global-variables,
         -cppcoreguidelines-macro-usage,
         -cppcoreguidelines-non-private-member-variables-in-classes,
         -cppcoreguidelines-owning-memory,
         -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
         -cppcoreguidelines-pro-bounds-constant-array-index,
         -cppcoreguidelines-pro-bounds-pointer-arithmetic,
         -cppcoreguidelines-pro-type-member-init,
         -cppcoreguidelines-pro-type-reinterpret-cast,
         -cppcoreguidelines-pro-type-union-access,
         -cppcoreguidelines-virtual-class-destructor,
         -fuchsia-default-arguments-calls,
         -fuchsia-default-arguments-declarations,
         -fuchsia-overloaded-operator,
         -google-build-using-namespace,
         -google-explicit-constructor,
         -google-readability-braces-around-statements,
         -google-readability-function-size,
         -google-readability-namespace-comments,
         -google-readability-todo,
         -google-runtime-int,
         -google-runtime-references,
         -hicpp-braces-around-statements,
         -hicpp-explicit-conversions,
         -hicpp-function-size,
         -hicpp-member-init,
         -hicpp-named-parameter,
         -hicpp-no-array-decay,
         -hicpp-no-assembler,
         -hicpp-signed-bitwise,
         -hicpp-uppercase-literal-suffix,
         -llvm-header-guard,
         -llvm-include-order,
         -llvm-namespace-comment,
         -llvmlibc-*,
         -misc-no-recursion,
         -misc-non-private-member-variables-in-classes,
         -modernize-concat-nested-namespaces,
         -modernize-use-default-member-init,
         -modernize-use-nodiscard,
         -modernize-use-trailing-return-type,
         -readability-braces-around-statements,
         -readability-function-cognitive-complexity,
         -readability-function-size,
         -readability-identifier-length,
         -readability-implicit-bool-conversion,
         -readability-magic-numbers,
         -readability-named-parameter,
         -readability-redundant-access-specifiers,
         -readability-uppercase-literal-suffix'

WarningsAsErrors: '*'

HeaderFilterRegex: '.*h$'
