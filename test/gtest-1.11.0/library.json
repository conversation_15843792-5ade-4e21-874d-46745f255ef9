{"name": "googletest", "keywords": "unittest, unit, test, gtest, gmock", "description": "googletest is a testing framework developed by the Testing Technology team with Google's specific requirements and constraints in mind. No matter whether you work on Linux, Windows, or a Mac, if you write C++ code, googletest can help you. And it supports any kind of tests, not just unit tests.", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/google/googletest/blob/master/README.md", "repository": {"type": "git", "url": "https://github.com/google/googletest.git"}, "version": "1.10.0", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["espressif32", "espressif8266"], "export": {"include": ["googlemock/include/*", "googlemock/src/*", "googletest/include/*", "googletest/src/*"], "exclude": ["ci", "googlemock/cmake", "googlemock/scripts", "googlemock/test", "googlemock/CMakeLists.txt", "googletest/cmake", "googletest/scripts", "googletest/test", "googletest/CMakeLists.txt"]}, "build": {"flags": ["-Igooglemock/include", "-Igooglemock", "-Igoogletest/include", "-Igoogletest"], "srcFilter": ["+<*>", "-<.git/>", "-<googlemock>", "-<googlemock/test/>", "-<googlemock/src>", "+<googlemock/src/gmock-all.cc>", "+<googletest/src/gtest-all.cc>", "+<googlemock/src/gmock_main.cc>", "-<googletest>", "-<googletest/codegear/>", "-<googletest/samples>", "-<googletest/test/>", "-<googletest/xcode>", "-<googletest/src>", "+<googletest/src/gtest-all.cc>", "+<googletest/src/gtest_main.cc>"]}}