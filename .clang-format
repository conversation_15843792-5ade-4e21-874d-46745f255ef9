# https://clang.llvm.org/docs/ClangFormatStyleOptions.html

DisableFormat: false
Language: Cpp
Standard: Cpp11
IndentWidth: 4
TabWidth: 4
UseTab: Never
ColumnLimit: 100
ReflowComments: false
SortIncludes: false

AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: true
AlignEscapedNewlinesLeft: false
AlignOperands: true
AlignTrailingComments: true
AlignAfterOpenBracket: true
DerivePointerAlignment: false
PointerAlignment: Left
IndentCaseLabels: true
ContinuationIndentWidth: 8
NamespaceIndentation: Inner
CompactNamespaces: true
FixNamespaceComments: true
AccessModifierOffset: -4

SpaceAfterControlStatementKeyword: false
SpaceAfterCStyleCast: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: Never
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInContainerLiterals: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
Cpp11BracedListStyle: true

KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 1
BinPackArguments: true
BinPackParameters: true
AlwaysBreakAfterReturnType: None
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakTemplateDeclarations: true
BreakConstructorInitializersBeforeComma: true
ConstructorInitializerAllOnOneLineOrOnePerLine: false
ConstructorInitializerIndentWidth: 8

AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AllowShortBlocksOnASingleLine: true
AllowShortCaseLabelsOnASingleLine: true
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortFunctionsOnASingleLine: All

BreakBeforeBinaryOperators: false
BreakBeforeTernaryOperators: false
BreakStringLiterals: true
BreakBeforeBraces: Custom
BraceWrapping:
    AfterClass: true
    AfterEnum: true
    AfterNamespace: false
    AfterStruct: true
    AfterUnion: true
    
    BeforeCatch: false
    BeforeElse: false
    IndentBraces: false
    AfterFunction: false
    AfterControlStatement: false

# penalties not thought of yet
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 60
PenaltyBreakString: 1000
PenaltyBreakFirstLessLess: 120
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 1000
