<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="same_tests_multiple_configurations">
  <Options order_by="file" rand_seed="0" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="[return42] bartest" filename="foo.h" line="0">
      <Message type="WARNING" filename="foo.h" line="0">
        <Text/>
        <Info>
          Running [return42] bartest
        </Info>
      </Message>
      <OverallResultsAsserts successes="1" failures="0"/>
    </TestCase>
    <TestCase name="[default] bartest" filename="foo.h" line="0">
      <Message type="WARNING" filename="foo.h" line="0">
        <Text/>
        <Info>
          Running [default] bartest
        </Info>
      </Message>
      <OverallResultsAsserts successes="1" failures="0"/>
    </TestCase>
    <TestCase name="[default] commontest" filename="foo.h" line="0">
      <Message type="WARNING" filename="foo.h" line="0">
        <Text/>
        <Info>
          Running [default] commontest
        </Info>
      </Message>
      <OverallResultsAsserts successes="0" failures="0"/>
    </TestCase>
    <TestCase name="[return42] commontest" filename="foo.h" line="0">
      <Message type="WARNING" filename="foo.h" line="0">
        <Text/>
        <Info>
          Running [return42] commontest
        </Info>
      </Message>
      <OverallResultsAsserts successes="0" failures="0"/>
    </TestCase>
    <TestCase name="main" filename="main.cpp" line="0">
      <Message type="WARNING" filename="main.cpp" line="0">
        <Text>
          hello from &lt;main.cpp>
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0"/>
    </TestCase>
    <TestCase name="test_runner" filename="test_runner.cpp" line="0">
      <Message type="WARNING" filename="test_runner.cpp" line="0">
        <Text>
          hello from &lt;test_runner.cpp>
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="2" failures="0"/>
  <OverallResultsTestCases successes="6" failures="0" skipped="0"/>
</doctest>
