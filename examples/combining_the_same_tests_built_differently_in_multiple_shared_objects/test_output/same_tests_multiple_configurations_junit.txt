<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="same_tests_multiple_configurations" errors="0" failures="0" tests="2">
    <testcase classname="foo.h" name="[return42] bartest" status="run"/>
    <testcase classname="foo.h" name="[default] bartest" status="run"/>
    <testcase classname="foo.h" name="[default] commontest" status="run"/>
    <testcase classname="foo.h" name="[return42] commontest" status="run"/>
    <testcase classname="main.cpp" name="main" status="run"/>
    <testcase classname="test_runner.cpp" name="test_runner" status="run"/>
  </testsuite>
</testsuites>
