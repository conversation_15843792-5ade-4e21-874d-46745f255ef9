[doctest] listing all test case names
===============================================================================
exercising tricky code paths of doctest
will end from a std::string exception
will end from a const char* exception
will end from an unknown exception
===============================================================================
[doctest] unskipped test cases passing the current filters: 4
