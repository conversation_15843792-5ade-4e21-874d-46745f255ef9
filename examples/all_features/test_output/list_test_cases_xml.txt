<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestCase name="exercising tricky code paths of doctest" filename="coverage_maxout.cpp" line="0" skipped="false"/>
  <TestCase name="will end from a std::string exception" testsuite="exception related" filename="coverage_maxout.cpp" line="0" skipped="false"/>
  <TestCase name="will end from a const char* exception" testsuite="exception related" filename="coverage_maxout.cpp" line="0" skipped="false"/>
  <TestCase name="will end from an unknown exception" testsuite="exception related" filename="coverage_maxout.cpp" line="0" skipped="false"/>
  <OverallResultsTestCases unskipped="4"/>
</doctest>
