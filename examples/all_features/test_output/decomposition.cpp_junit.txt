<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="all_features" errors="0" failures="2" tests="4">
    <testcase classname="decomposition.cpp" name="Move Only Type" status="run">
      <failure message="{?}" type="CHECK">
decomposition.cpp(0):
CHECK( genType(false) ) is NOT correct!
  values: CHECK( {?} )

      </failure>
      <failure message="{?}" type="CHECK">
decomposition.cpp(0):
CHECK( a ) is NOT correct!
  values: CHECK( {?} )

      </failure>
    </testcase>
    <testcase classname="decomposition.cpp" name="Impl cast from non-const value" status="run"/>
  </testsuite>
</testsuites>
Program code.
