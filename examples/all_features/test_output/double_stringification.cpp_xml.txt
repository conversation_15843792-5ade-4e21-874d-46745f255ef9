<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="toString std::string ret type" filename="double_stringification.cpp" line="0">
      <OverallResultsAsserts successes="3" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="3" failures="0"/>
  <OverallResultsTestCases successes="1" failures="0"/>
</doctest>
Program code.
