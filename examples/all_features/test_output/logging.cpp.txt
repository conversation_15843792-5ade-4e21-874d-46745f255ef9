[doctest] run with "--help" for options
===============================================================================
logging.cpp(0):
TEST CASE:  logging the counter of a loop

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 1 != 1 )
  logged: current iteration of loop:
          i := 0

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 2 != 2 )
  logged: current iteration of loop:
          i := 1

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 4 != 4 )
  logged: current iteration of loop:
          i := 2

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 8 != 8 )
  logged: current iteration of loop:
          i := 3

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 16 != 16 )
  logged: current iteration of loop:
          i := 4

===============================================================================
logging.cpp(0):
TEST CASE:  a test case that will end from an exception

logging.cpp(0): ERROR: forcing the many captures to be stringified
  logged: lots of captures: 42 42 42;
          old way of capturing - using the streaming operator: 42 42

logging.cpp(0): ERROR: CHECK( some_var == 666 ) is NOT correct!
  values: CHECK( 42 == 666 )
  logged: someTests() returned: 42
          this should be printed if an exception is thrown even if no assert has failed: 42
          in a nested scope this should be printed as well: 42
          why is this not 666 ?!

logging.cpp(0): ERROR: test case THREW exception: 0
  logged: someTests() returned: 42
          this should be printed if an exception is thrown even if no assert has failed: 42
          in a nested scope this should be printed as well: 42

===============================================================================
logging.cpp(0):
TEST CASE:  a test case that will end from an exception and should print the unprinted context

logging.cpp(0): ERROR: test case THREW exception: 0
  logged: should be printed even if an exception is thrown and no assert fails before that

===============================================================================
logging.cpp(0):
TEST CASE:  third party asserts can report failures to doctest

logging.cpp(0): ERROR: MY_ASSERT(false)

logging.cpp(0): FATAL ERROR: MY_ASSERT_FATAL(false)

===============================================================================
logging.cpp(0):
TEST CASE:  explicit failures 1

logging.cpp(0): ERROR: this should not end the test case, but mark it as failing

logging.cpp(0): MESSAGE: reached!

===============================================================================
logging.cpp(0):
TEST CASE:  explicit failures 2

logging.cpp(0): FATAL ERROR: fail the test case and also end it

===============================================================================
[doctest] test cases:  6 | 0 passed |  6 failed |
[doctest] assertions: 11 | 0 passed | 11 failed |
[doctest] Status: FAILURE!
Program code.
