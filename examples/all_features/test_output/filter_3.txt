[doctest] run with "--help" for options

root

root
outside of subcase
outside of subcase
outside of subcase
===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...

subcases.cpp(0): MESSAGE: print me twice

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...
  sc1

subcases.cpp(0): MESSAGE: hello! from sc1

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  from function...
  sc1

subcases.cpp(0): MESSAGE: lala

===============================================================================
subcases.cpp(0):
TEST SUITE: with a funny name,
TEST CASE:  without a funny name:

subcases.cpp(0): MESSAGE: Nooo

===============================================================================
[doctest] test cases: 10 | 10 passed | 0 failed |
[doctest] assertions:  0 |  0 passed | 0 failed |
[doctest] Status: SUCCESS!
Program code.
