[doctest] run with "--help" for options
===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  vector stuff<std::vector<int>>

templated_test_cases.cpp(0): ERROR: CHECK( vec.size() == 20 ) is NOT correct!
  values: CHECK( 10 == 20 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<Custom name test>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<Other custom name>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<TypePair<bool, int>>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  bad stringification of type pair<int_pair>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
[doctest] test cases: 15 | 10 passed | 5 failed |
[doctest] assertions: 19 | 14 passed | 5 failed |
[doctest] Status: FAILURE!
Program code.
