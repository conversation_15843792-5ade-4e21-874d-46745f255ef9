<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="lots of nested subcases" filename="subcases.cpp" line="0">

root
      <SubCase filename="subcases.cpp" line="0">
1
        <SubCase filename="subcases.cpp" line="0">
1.1
        </SubCase>
      </SubCase>

root
      <SubCase filename="subcases.cpp" line="0">
2
        <SubCase filename="subcases.cpp" line="0">
2.1
        </SubCase>
      </SubCase>

root
      <SubCase filename="subcases.cpp" line="0">
2
        <SubCase filename="subcases.cpp" line="0">
          <Message type="FATAL ERROR" filename="subcases.cpp" line="0">
            <Text/>
          </Message>
        </SubCase>
      </SubCase>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="false"/>
    </TestCase>
    <TestCase name="reentering subcase via regular control flow" filename="subcases.cpp" line="0">

root
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside subcase 0
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside subcase 1
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside subcase 2
      </SubCase>

root
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
also inside 0
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
also inside 1
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
also inside 2
      </SubCase>

root
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
fail inside 0
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
        <Message type="FATAL ERROR" filename="subcases.cpp" line="0">
          <Text>
            1
          </Text>
        </Message>
      </SubCase>

root
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
nested twice 0, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 0, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 0, 2
        </SubCase>
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
nested twice 1, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 1, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 1, 2
        </SubCase>
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
nested twice 2, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 2, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
nested twice 2, 2
        </SubCase>
      </SubCase>

root
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
also twice 0, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 0, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 0, 2
        </SubCase>
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
also twice 1, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 1, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 1, 2
        </SubCase>
      </SubCase>
outside of subcase
      <SubCase filename="subcases.cpp" line="0">
inside outside
        <SubCase filename="subcases.cpp" line="0">
also twice 2, 0
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 2, 1
        </SubCase>
        <SubCase filename="subcases.cpp" line="0">
also twice 2, 2
        </SubCase>
      </SubCase>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="false"/>
    </TestCase>
    <TestCase name="subcases can be used in a separate function as well" filename="subcases.cpp" line="0">
      <SubCase name="from function..." filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            print me twice
          </Text>
        </Message>
        <SubCase name="sc1" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              hello! from sc1
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <Message type="WARNING" filename="subcases.cpp" line="0">
        <Text>
          lala
        </Text>
      </Message>
      <SubCase name="from function..." filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            print me twice
          </Text>
        </Message>
        <SubCase name="sc2" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              hello! from sc2
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <Message type="WARNING" filename="subcases.cpp" line="0">
        <Text>
          lala
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="  Scenario: vectors can be sized and resized" filename="subcases.cpp" line="0">
      <SubCase name="   Given: A vector with some items" filename="subcases.cpp" line="0">
        <SubCase name="    When: the size is increased" filename="subcases.cpp" line="0">
          <SubCase name="    Then: the size and capacity change" filename="subcases.cpp" line="0">
            <Expression success="false" type="CHECK" filename="subcases.cpp" line="0">
              <Original>
                v.size() == 20
              </Original>
              <Expanded>
                10 == 20
              </Expanded>
            </Expression>
          </SubCase>
        </SubCase>
      </SubCase>
      <SubCase name="   Given: A vector with some items" filename="subcases.cpp" line="0">
        <SubCase name="    When: the size is reduced" filename="subcases.cpp" line="0">
          <SubCase name="    Then: the size changes but not capacity" filename="subcases.cpp" line="0">
          </SubCase>
        </SubCase>
      </SubCase>
      <SubCase name="   Given: A vector with some items" filename="subcases.cpp" line="0">
        <SubCase name="    When: more capacity is reserved" filename="subcases.cpp" line="0">
          <SubCase name="    Then: the capacity changes but not the size" filename="subcases.cpp" line="0">
          </SubCase>
        </SubCase>
      </SubCase>
      <SubCase name="   Given: A vector with some items" filename="subcases.cpp" line="0">
        <SubCase name="    When: less capacity is reserved" filename="subcases.cpp" line="0">
          <SubCase name="    Then: neither size nor capacity are changed" filename="subcases.cpp" line="0">
            <Expression success="false" type="CHECK" filename="subcases.cpp" line="0">
              <Original>
                v.size() == 10
              </Original>
              <Expanded>
                5 == 10
              </Expanded>
            </Expression>
          </SubCase>
        </SubCase>
      </SubCase>
      <OverallResultsAsserts successes="14" failures="2" test_case_success="false"/>
    </TestCase>
    <TestCase name="test case should fail even though the last subcase passes" filename="subcases.cpp" line="0">
      <SubCase name="one" filename="subcases.cpp" line="0">
        <Expression success="false" type="CHECK" filename="subcases.cpp" line="0">
          <Original>
            false
          </Original>
          <Expanded>
            false
          </Expanded>
        </Expression>
      </SubCase>
      <SubCase name="two" filename="subcases.cpp" line="0">
      </SubCase>
      <OverallResultsAsserts successes="1" failures="1" test_case_success="false"/>
    </TestCase>
    <TestCase name="fails from an exception but gets re-entered to traverse all subcases" filename="subcases.cpp" line="0">
      <SubCase name="level zero" filename="subcases.cpp" line="0">
        <SubCase name="one" filename="subcases.cpp" line="0">
          <Expression success="false" type="CHECK" filename="subcases.cpp" line="0">
            <Original>
              false
            </Original>
            <Expanded>
              false
            </Expanded>
          </Expression>
        </SubCase>
        <Exception crash="false">
          exception thrown in subcase - will translate later when the whole test case has been exited (cannot translate while there is an active exception)
        </Exception>
      </SubCase>
      <Exception crash="false">
        failure... but the show must go on!
      </Exception>
      <SubCase name="level zero" filename="subcases.cpp" line="0">
        <SubCase name="two" filename="subcases.cpp" line="0">
          <Expression success="false" type="CHECK" filename="subcases.cpp" line="0">
            <Original>
              false
            </Original>
            <Expanded>
              false
            </Expanded>
          </Expression>
        </SubCase>
        <Exception crash="false">
          exception thrown in subcase - will translate later when the whole test case has been exited (cannot translate while there is an active exception)
        </Exception>
      </SubCase>
      <Exception crash="false">
        failure... but the show must go on!
      </Exception>
      <OverallResultsAsserts successes="0" failures="2" test_case_success="false"/>
    </TestCase>
    <TestCase name="Nested - related to https://github.com/doctest/doctest/issues/282" filename="subcases.cpp" line="0">
      <SubCase name="generate data variant 1" filename="subcases.cpp" line="0">
        <SubCase name="check data 1" filename="subcases.cpp" line="0">
        </SubCase>
      </SubCase>
      <SubCase name="generate data variant 1" filename="subcases.cpp" line="0">
        <SubCase name="check data 2" filename="subcases.cpp" line="0">
        </SubCase>
      </SubCase>
      <SubCase name="generate data variant 1" filename="subcases.cpp" line="0">
        <SubCase name="check data 1" filename="subcases.cpp" line="0">
        </SubCase>
      </SubCase>
      <SubCase name="generate data variant 1" filename="subcases.cpp" line="0">
        <SubCase name="check data 2" filename="subcases.cpp" line="0">
        </SubCase>
      </SubCase>
      <OverallResultsAsserts successes="4" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="subcases with changing names" filename="subcases.cpp" line="0">
      <SubCase name="outer 0" filename="subcases.cpp" line="0">
        <SubCase name="inner 0" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              msg!
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <SubCase name="outer 0" filename="subcases.cpp" line="0">
        <SubCase name="inner 1" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              msg!
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <SubCase name="outer 1" filename="subcases.cpp" line="0">
        <SubCase name="inner 0" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              msg!
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <SubCase name="outer 1" filename="subcases.cpp" line="0">
        <SubCase name="inner 1" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              msg!
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <SubCase name="separate" filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            separate msg!
          </Text>
        </Message>
      </SubCase>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <TestSuite name="with a funny name,">
    <TestCase name="with a funnier name\:" filename="subcases.cpp" line="0">
      <SubCase name="with the funniest name\," filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            Yes!
          </Text>
        </Message>
      </SubCase>
      <SubCase name="with a slightly funny name :" filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            Yep!
          </Text>
        </Message>
      </SubCase>
      <SubCase name="without a funny name" filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            NO!
          </Text>
        </Message>
      </SubCase>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="without a funny name:" filename="subcases.cpp" line="0">
      <Message type="WARNING" filename="subcases.cpp" line="0">
        <Text>
          Nooo
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="19" failures="7"/>
  <OverallResultsTestCases successes="5" failures="5"/>
</doctest>
Program code.
