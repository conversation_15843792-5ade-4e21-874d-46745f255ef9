<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="exercising tricky code paths of doctest" filename="coverage_maxout.cpp" line="0">
      <Expression success="false" type="CHECK" filename="coverage_maxout.cpp" line="0">
        <Original>
          str.compare(const_str, true) != 0
        </Original>
        <Expanded>
          0 != 0
        </Expanded>
        <Info>
          should fail
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="coverage_maxout.cpp" line="0">
        <Original>
          str.compare("omgomgomg", false) != 0
        </Original>
        <Expanded>
          0 != 0
        </Expanded>
        <Info>
          should fail
        </Info>
      </Expression>
      <Expression success="false" type="CHECK_FALSE" filename="coverage_maxout.cpp" line="0">
        <Original>
          isThereAnything
        </Original>
        <Expanded>
          true
        </Expanded>
        <Info>
          should fail
        </Info>
      </Expression>
      <OverallResultsAsserts successes="28" failures="3" test_case_success="false"/>
    </TestCase>
  </TestSuite>
  <TestSuite name="exception related">
    <TestCase name="will end from a std::string exception" filename="coverage_maxout.cpp" line="0">
      <Exception crash="false">
        std::string!
      </Exception>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="false"/>
    </TestCase>
    <TestCase name="will end from a const char* exception" filename="coverage_maxout.cpp" line="0">
      <Exception crash="false">
        const char*!
      </Exception>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="false"/>
    </TestCase>
    <TestCase name="will end from an unknown exception" filename="coverage_maxout.cpp" line="0">
      <Exception crash="false">
        unknown exception
      </Exception>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="false"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="28" failures="3"/>
  <OverallResultsTestCases successes="0" failures="4"/>
</doctest>
Program code.
