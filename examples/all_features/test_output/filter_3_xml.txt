<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="lots of nested subcases" filename="subcases.cpp" line="0">

root
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="reentering subcase via regular control flow" filename="subcases.cpp" line="0">

root
outside of subcase
outside of subcase
outside of subcase
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="subcases can be used in a separate function as well" filename="subcases.cpp" line="0">
      <SubCase name="from function..." filename="subcases.cpp" line="0">
        <Message type="WARNING" filename="subcases.cpp" line="0">
          <Text>
            print me twice
          </Text>
        </Message>
        <SubCase name="sc1" filename="subcases.cpp" line="0">
          <Message type="WARNING" filename="subcases.cpp" line="0">
            <Text>
              hello! from sc1
            </Text>
          </Message>
        </SubCase>
      </SubCase>
      <Message type="WARNING" filename="subcases.cpp" line="0">
        <Text>
          lala
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="  Scenario: vectors can be sized and resized" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="test case should fail even though the last subcase passes" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="fails from an exception but gets re-entered to traverse all subcases" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="Nested - related to https://github.com/doctest/doctest/issues/282" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="subcases with changing names" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <TestSuite name="with a funny name,">
    <TestCase name="with a funnier name\:" filename="subcases.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="without a funny name:" filename="subcases.cpp" line="0">
      <Message type="WARNING" filename="subcases.cpp" line="0">
        <Text>
          Nooo
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="0" failures="0"/>
  <OverallResultsTestCases successes="10" failures="0"/>
</doctest>
Program code.
