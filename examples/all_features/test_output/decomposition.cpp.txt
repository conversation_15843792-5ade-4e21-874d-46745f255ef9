[doctest] run with "--help" for options
===============================================================================
decomposition.cpp(0):
TEST CASE:  Move Only Type

decomposition.cpp(0): ERROR: CHECK( genType(false) ) is NOT correct!
  values: CHECK( {?} )

decomposition.cpp(0): ERROR: CHECK( a ) is NOT correct!
  values: CHECK( {?} )

===============================================================================
[doctest] test cases: 2 | 1 passed | 1 failed |
[doctest] assertions: 4 | 2 passed | 2 failed |
[doctest] Status: FAILURE!
Program code.
