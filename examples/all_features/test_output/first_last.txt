[doctest] run with "--help" for options
===============================================================================
coverage_maxout.cpp(0):
TEST SUITE: exception related
TEST CASE:  will end from a std::string exception

coverage_maxout.cpp(0): ERROR: test case THREW exception: std::string!

===============================================================================
coverage_maxout.cpp(0):
TEST SUITE: exception related
TEST CASE:  will end from a const char* exception

coverage_maxout.cpp(0): ERROR: test case THREW exception: const char*!

===============================================================================
coverage_maxout.cpp(0):
TEST SUITE: exception related
TEST CASE:  will end from an unknown exception

coverage_maxout.cpp(0): ERROR: test case THREW exception: unknown exception

===============================================================================
[doctest] test cases: 4 | 1 passed | 3 failed |
[doctest] assertions: 0 | 0 passed | 0 failed |
[doctest] Status: FAILURE!
Program code.
