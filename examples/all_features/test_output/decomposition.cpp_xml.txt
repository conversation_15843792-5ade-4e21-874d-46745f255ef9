<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="Move Only Type" filename="decomposition.cpp" line="0">
      <Expression success="false" type="CHECK" filename="decomposition.cpp" line="0">
        <Original>
          genType(false)
        </Original>
        <Expanded>
          {?}
        </Expanded>
      </Expression>
      <Expression success="false" type="CHECK" filename="decomposition.cpp" line="0">
        <Original>
          a
        </Original>
        <Expanded>
          {?}
        </Expanded>
      </Expression>
      <OverallResultsAsserts successes="1" failures="2" test_case_success="false"/>
    </TestCase>
    <TestCase name="Impl cast from non-const value" filename="decomposition.cpp" line="0">
      <OverallResultsAsserts successes="1" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="2" failures="2"/>
  <OverallResultsTestCases successes="1" failures="1"/>
</doctest>
Program code.
