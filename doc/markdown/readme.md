Reference
=======

Project:

- [Features and design goals](features.md) - the complete list of features
- [Community driven roadmap](https://github.com/doctest/doctest/issues/600) - upcoming features
- [Benchmarks](benchmarks.md) - compile-time and runtime supremacy
- [Contributing](../../CONTRIBUTING.md) - how to make a proper pull request
- [Changelog](../../CHANGELOG.md) - generated changelog based on closed issues/PRs

Usage:

- [Tutorial](tutorial.md) - make sure you have read it before the other parts of the documentation
- [Assertion macros](assertions.md)
- [Test cases, subcases and test fixtures](testcases.md)
- [Parameterized test cases](parameterized-tests.md)
- [Logging macros](logging.md)
- [Command line](commandline.md)
- [```main()``` entry point](main.md)
- [Configuration](configuration.md)
- [String conversions](stringification.md)
- [Reporters](reporters.md)
- [Extensions](extensions.md)
- [FAQ](faq.md)
- [Build systems](build-systems.md)
- [Examples](../../examples)

This library is free, and will stay free but needs your support to sustain its development. There are lots of [**new features**](https://github.com/doctest/doctest/issues/600) and maintenance to do. If you work for a company using **doctest** or have the means to do so, please consider financial support.

[![Patreon](https://cloud.githubusercontent.com/assets/8225057/5990484/70413560-a9ab-11e4-8942-1a63607c0b00.png)](http://www.patreon.com/onqtam)
[![PayPal](https://www.paypalobjects.com/en_US/i/btn/btn_donate_LG.gif)](https://www.paypal.me/onqtam/10)

------------

<p align="center"><img src="../../scripts/data/logo/icon_2.svg"></p>
