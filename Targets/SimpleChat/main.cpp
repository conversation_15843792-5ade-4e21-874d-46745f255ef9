//
//  Copyright © 2024-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
// External includes
#include <llama.h>
// System includes
#include <algorithm>
#include <cstdlib>
#include <cstring>
#include <exception>
#include <iostream>
#include <optional>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

namespace SimpleChat
{

// Command line arguments parser.
class CommandLineParser
{
public:
    CommandLineParser(const int argc, char** argv) : m_programName(argv[0])
    {
        for (int i = 1; i < argc; ++i)
        {
            if (i + 1 < argc && argv[i][0] == '-')
            {
                m_args[argv[i]] = argv[i + 1];
                ++i;
            }
            else if (argv[i][0] == '-')
            {
                m_args[argv[i]] = "";
            }
        }
    }

    std::optional<std::string> getOption(const std::string& option) const
    {
        auto it = m_args.find(option);
        if (it != m_args.end())
        {
            return it->second;
        }
        return std::nullopt;
    }

    bool hasOption(const std::string& option) const
    {
        return m_args.contains(option);
    }

    std::string getProgramName() const
    {
        return m_programName;
    }

    void showHelp() const
    {
        std::cout << "\nExample usage:\n";
        std::cout << "\n    " << m_programName << " -m model.gguf [-c context_size] [-ngl n_gpu_layers]\n";
        std::cout << "\nOptions:\n";
        std::cout << "  -m <path>          Path to the model file (required)\n";
        std::cout << "  -c <size>          Context size (Default: 2048)\n";
        std::cout << "  -ngl <layers>      Number of GPU layers (Default: 99)\n";
        std::cout << "\n";
    }

private:
    std::string m_programName;
    std::unordered_map<std::string, std::string> m_args;
};


class Model
{
public:
    Model(const std::string& modelPath, int nGpuLayers)
    {
        // Load dynamic backends
        ggml_backend_load_all();

        // Initialize model parameters
        llama_model_params modelParams = llama_model_default_params();
        modelParams.n_gpu_layers = nGpuLayers;

        // Load the model
        m_model = llama_model_load_from_file(modelPath.c_str(), modelParams);
        if (!m_model)
        {
            throw std::runtime_error("Failed to load model from " + modelPath);
        }
    }

    ~Model()
    {
        if (m_model)
        {
            llama_model_free(m_model);
        }
    }

    // Prevent copying
    Model(const Model&) = delete;
    Model& operator=(const Model&) = delete;

    // Allow moving
    Model(Model&& other) noexcept : m_model(other.m_model)
    {
        other.m_model = nullptr;
    }

    Model& operator=(Model&& other) noexcept
    {
        if (this != &other)
        {
            if (m_model)
            {
                llama_model_free(m_model);
            }
            m_model = other.m_model;
            other.m_model = nullptr;
        }
        return *this;
    }

    llama_model* get() const
    {
        return m_model;
    }

    const llama_vocab* getVocab() const
    {
        return llama_model_get_vocab(m_model);
    }

    const char* getChatTemplate() const
    {
        return llama_model_chat_template(m_model, nullptr);
    }

private:
    llama_model* m_model = nullptr;
};


// RAII wrapper for llama_context.
class Context
{
public:
    Context(const Model& model, const int contextSize)
    {
        // Initialize context parameters
        llama_context_params ctxParams = llama_context_default_params();
        ctxParams.n_ctx   = contextSize;
        ctxParams.n_batch = contextSize;

        // Create context
        m_context = llama_init_from_model(model.get(), ctxParams);
        if (!m_context)
        {
            throw std::runtime_error("Failed to create llama context.");
        }
    }

    ~Context()
    {
        if (m_context)
        {
            llama_free(m_context);
        }
    }

    // Prevent copying
    Context(const Context&) = delete;
    Context& operator=(const Context&) = delete;

    // Allow moving
    Context(Context&& other) noexcept : m_context(other.m_context)
    {
        other.m_context = nullptr;
    }

    Context& operator=(Context&& other) noexcept
    {
        if (this != &other)
        {
            if (m_context)
            {
                llama_free(m_context);
            }
            m_context = other.m_context;
            other.m_context = nullptr;
        }
        return *this;
    }

    llama_context* get() const
    {
        return m_context;
    }

    int getContextSize() const
    {
        return llama_n_ctx(m_context);
    }

    int getUsedCells() const
    {
        return llama_get_kv_cache_used_cells(m_context);
    }

private:
    llama_context* m_context = nullptr;
};


// RAII wrapper for llama_sampler.
class Sampler
{
public:
    Sampler()
    {
        m_sampler = llama_sampler_chain_init(llama_sampler_chain_default_params());
        if (!m_sampler)
        {
            throw std::runtime_error("Failed to initialize sampler chain.");
        }

        // Add sampling strategies
        llama_sampler_chain_add(m_sampler, llama_sampler_init_min_p(0.05f, 1));
        llama_sampler_chain_add(m_sampler, llama_sampler_init_temp(0.8f));
        llama_sampler_chain_add(m_sampler, llama_sampler_init_dist(LLAMA_DEFAULT_SEED));
    }

    ~Sampler()
    {
        if (m_sampler)
        {
            llama_sampler_free(m_sampler);
        }
    }

    // Prevent copying
    Sampler(const Sampler&) = delete;
    Sampler& operator=(const Sampler&) = delete;

    // Allow moving
    Sampler(Sampler&& other) noexcept : m_sampler(other.m_sampler)
    {
        other.m_sampler = nullptr;
    }

    Sampler& operator=(Sampler&& other) noexcept
    {
        if (this != &other)
        {
            if (m_sampler)
            {
                llama_sampler_free(m_sampler);
            }
            m_sampler = other.m_sampler;
            other.m_sampler = nullptr;
        }
        return *this;
    }

    llama_sampler* get() const
    {
        return m_sampler;
    }

private:
    llama_sampler* m_sampler = nullptr;
};


//Main chat application class.
class ChatApplication
{
public:
    ChatApplication(int argc, char** argv)
    {
        // Set up logging
        setupLogging();

        // Parse command line arguments
        CommandLineParser parser(argc, argv);

        // Check for required model path
        auto modelPath = parser.getOption("-m");
        if (!modelPath)
        {
            parser.showHelp();
            throw std::runtime_error("Model path is required.");
        }

        // Get optional parameters with defaults
        int contextSize = 2048;
        if (auto ctx = parser.getOption("-c"))
        {
            try
            {
                contextSize = std::stoi(*ctx);
            }
            catch (const std::exception& e)
            {
                throw std::runtime_error("Invalid context size: " + *ctx);
            }
        }

        int gpuLayers = 99;
        if (auto ngl = parser.getOption("-ngl"))
        {
            try
            {
                gpuLayers = std::stoi(*ngl);
            }
            catch (const std::exception& e)
            {
                throw std::runtime_error("Invalid GPU layers: " + *ngl);
            }
        }

        // Initialize components
        m_model = std::make_unique<Model>(*modelPath, gpuLayers);
        m_context = std::make_unique<Context>(*m_model, contextSize);
        m_sampler = std::make_unique<Sampler>();

        // Initialize message buffer
        m_formattedBuffer.resize(m_context->getContextSize());
    }

    void run()
    {
        std::vector<llama_chat_message> messages;
        int prevLen = 0;

        while (true)
        {
            // Get user input
            std::cout << "\033[32m> \033[0m";
            std::string userInput;
            std::getline(std::cin, userInput);

            // Exit on empty input
            if (userInput.empty())
            {
                break;
            }

            try
            {
                // Process user message
                const char* chatTemplate = m_model->getChatTemplate();

                // Add user message to history
                messages.push_back({"user", strdup(userInput.c_str())});

                // Apply chat template
                int newLen = llama_chat_apply_template(chatTemplate,
                                                       messages.data(),
                                                       messages.size(),
                                                       true,
                                                       m_formattedBuffer.data(),
                                                       m_formattedBuffer.size());

                // Resize buffer if needed
                if (newLen > static_cast<int>(m_formattedBuffer.size()))
                {
                    m_formattedBuffer.resize(newLen);
                    newLen = llama_chat_apply_template(chatTemplate,
                                                       messages.data(),
                                                       messages.size(),
                                                       true,
                                                       m_formattedBuffer.data(),
                                                       m_formattedBuffer.size());
                }

                if (newLen < 0)
                {
                    throw std::runtime_error("Failed to apply chat template.");
                }

                // Extract prompt for generation.
                std::string prompt(m_formattedBuffer.begin() + prevLen, m_formattedBuffer.begin() + newLen);

                // Generate response.
                std::cout << "\033[33m";
                std::string response = generateResponse(prompt);
                std::cout << "\n\033[0m";

                // Add assistant response to history.
                messages.push_back({"assistant", strdup(response.c_str())});

                // Update previous length for next iteration.
                prevLen = llama_chat_apply_template(chatTemplate, messages.data(), messages.size(), false, nullptr, 0);
                if (prevLen < 0)
                {
                    throw std::runtime_error("Failed to update chat template.");
                }
            }
            catch (const std::exception& e)
            {
                std::cerr << "Error: " << e.what() << std::endl;
            }
        }

        // Free message content
        for (auto & [role, content] : messages)
        {
            free(const_cast<char*>(content));
        }
    }

private:
    void setupLogging()
    {
        // Set up logging to only show errors
        llama_log_set([](const enum ggml_log_level level, const char* text, void*)
        {
            if (level >= GGML_LOG_LEVEL_ERROR)
            {
                std::cerr << text;
            }
        }, nullptr);
    }

    std::string generateResponse(const std::string& prompt) const
    {
        std::string response;
        const llama_vocab* vocab = m_model->getVocab();
        llama_context* ctx = m_context->get();

        // Check if this is the first prompt
        const bool isFirst = m_context->getUsedCells() == 0;

        // Tokenize the prompt
        const int nPromptTokens = -llama_tokenize(vocab, prompt.c_str(), prompt.size(), nullptr, 0, isFirst, true);
        std::vector<llama_token> promptTokens(nPromptTokens);

        if (llama_tokenize(vocab, prompt.c_str(), prompt.size(), promptTokens.data(), promptTokens.size(), isFirst, true) < 0)
        {
            throw std::runtime_error("Failed to tokenize prompt.");
        }

        // Prepare batch for the prompt
        llama_batch batch = llama_batch_get_one(promptTokens.data(), promptTokens.size());

        // Process tokens and generate response
        while (true)
        {
            // Check context space
            int contextSize = m_context->getContextSize();
            int usedCells = m_context->getUsedCells();

            if (usedCells + batch.n_tokens > contextSize)
            {
                std::cout << "\033[0m\n";
                throw std::runtime_error("Context size exceeded.");
            }

            // Decode batch
            if (llama_decode(ctx, batch))
            {
                throw std::runtime_error("Failed to decode batch.");
            }

            // Sample next token
            llama_token newTokenID = llama_sampler_sample(m_sampler->get(), ctx, -1);

            // Check for end of generation
            if (llama_vocab_is_eog(vocab, newTokenID))
            {
                break;
            }

            // Convert token to text
            char buf[256];
            int n = llama_token_to_piece(vocab, newTokenID, buf, sizeof(buf), 0, true);

            if (n < 0)
            {
                throw std::runtime_error("Failed to convert token to text.");
            }

            // Add piece to response and display
            std::string piece(buf, n);
            std::cout << piece;
            std::cout.flush();
            response += piece;

            // Prepare next batch with the sampled token
            batch = llama_batch_get_one(&newTokenID, 1);
        }

        return response;
    }

private:
    std::unique_ptr<Model> m_model;
    std::unique_ptr<Context> m_context;
    std::unique_ptr<Sampler> m_sampler;
    std::vector<char> m_formattedBuffer;
};

} // namespace SimpleChat


int main(const int argc, char** argv)
{
    try
    {
        SimpleChat::ChatApplication app(argc, argv);
        app.run();
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Unexpected error: " << e.what() << std::endl;
        return 1;
    }
}
