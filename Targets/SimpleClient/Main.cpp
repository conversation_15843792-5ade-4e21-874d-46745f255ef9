//
//  Copyright © 2024-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
// External includes
#include "rpc/client.h"
// System includes
#include <iostream>
#include <chrono>


void SimpleFuncCall(rpc::client& client, const size_t sampleCount = 1'000)
{
    int result = 0;
    const auto timeStart = std::chrono::steady_clock::now();

    // Calling a function with parameters and converting the result to int.
    for (size_t i=0; i<sampleCount; ++i)
    {
        result = client.call("add", 2, 3).as<int>();
    }

    const auto timeEnd = std::chrono::steady_clock::now();
    const auto duration = std::chrono::duration<double, std::milli>(timeEnd - timeStart).count();
    std::cout << std::format("SimpleFuncCall({}) - Duration: {} ms - Average: {} ms - Result: {}",
                             sampleCount, duration, duration / static_cast<double>(sampleCount), result) << std::endl;
}


void SendNBytesFuncCall(rpc::client& client, const size_t numBytes = 1024*1024, const size_t sampleCount = 1'000)
{
    int result = 0;
    const auto timeStart = std::chrono::steady_clock::now();
    std::vector<char> data(numBytes, 1);

    // Calling a function with parameters and converting the result to int.
    for (size_t i=0; i<sampleCount; ++i)
    {
        result = client.call("process_bytes", data).as<bool>();
    }

    const auto timeEnd = std::chrono::steady_clock::now();
    const auto duration = std::chrono::duration<double, std::milli>(timeEnd - timeStart).count();
    std::cout << std::format("SendNBytesFuncCall({}, {}) - Duration: {} ms - Average: {} ms - Result: {}",
                             numBytes, sampleCount, duration, duration / static_cast<double>(sampleCount), result)
                             << std::endl;
}


int main(int, char**)
{
    // Creating a client that connects to the localhost on port 8080.
    rpc::client client("127.0.0.1", 8080);

    SimpleFuncCall(client, 10'000);
    SendNBytesFuncCall(client, 1024*1024, 1'000);
    SendNBytesFuncCall(client, 1024*1024*1024, 3);

    return 0;
}
