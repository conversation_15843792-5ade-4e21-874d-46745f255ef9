token literal names:
null
';'
','
'='
'float'
'int'
'void'
'bool'
'('
')'
'{'
'}'
'if'
'else'
'while'
'do'
'return'
'continue'
'break'
'++'
'--'
'-'
'+'
'!'
'*'
'/'
'<'
'<='
'>'
'>='
'=='
'!='
'&&'
'||'
'for'
null
null
null
null
null
null
null

token symbolic names:
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
null
BOOL
INT
FLOAT
ID
WS
LINECOMMENT
BLOCKCOMMENT

rule names:
program
varDecl
varInit
type
functionDecl
formalParameters
formalParameter
block
stat
expr
exprList
assignment
forStatement
forVarDecl
forCondition
forIncrements
forInc


atn:
[4, 1, 41, 232, 2, 0, 7, 0, 2, 1, 7, 1, 2, 2, 7, 2, 2, 3, 7, 3, 2, 4, 7, 4, 2, 5, 7, 5, 2, 6, 7, 6, 2, 7, 7, 7, 2, 8, 7, 8, 2, 9, 7, 9, 2, 10, 7, 10, 2, 11, 7, 11, 2, 12, 7, 12, 2, 13, 7, 13, 2, 14, 7, 14, 2, 15, 7, 15, 2, 16, 7, 16, 1, 0, 1, 0, 1, 0, 1, 0, 4, 0, 39, 8, 0, 11, 0, 12, 0, 40, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 5, 1, 49, 8, 1, 10, 1, 12, 1, 52, 9, 1, 1, 2, 1, 2, 1, 2, 3, 2, 57, 8, 2, 1, 3, 1, 3, 1, 4, 1, 4, 1, 4, 1, 4, 3, 4, 65, 8, 4, 1, 4, 1, 4, 1, 4, 5, 4, 70, 8, 4, 10, 4, 12, 4, 73, 9, 4, 1, 4, 1, 4, 1, 5, 1, 5, 1, 5, 5, 5, 80, 8, 5, 10, 5, 12, 5, 83, 9, 5, 1, 6, 1, 6, 1, 6, 1, 7, 1, 7, 5, 7, 90, 8, 7, 10, 7, 12, 7, 93, 9, 7, 1, 7, 1, 7, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 3, 8, 108, 8, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 3, 8, 127, 8, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 1, 8, 3, 8, 140, 8, 8, 1, 9, 1, 9, 1, 9, 1, 9, 3, 9, 146, 8, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 3, 9, 165, 8, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 1, 9, 5, 9, 185, 8, 9, 10, 9, 12, 9, 188, 9, 9, 1, 10, 1, 10, 1, 10, 5, 10, 193, 8, 10, 10, 10, 12, 10, 196, 9, 10, 1, 11, 1, 11, 1, 11, 1, 11, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 13, 3, 13, 213, 8, 13, 1, 14, 3, 14, 216, 8, 14, 1, 15, 1, 15, 1, 15, 5, 15, 221, 8, 15, 10, 15, 12, 15, 224, 9, 15, 3, 15, 226, 8, 15, 1, 16, 1, 16, 3, 16, 230, 8, 16, 1, 16, 0, 1, 18, 17, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 0, 8, 1, 0, 4, 7, 2, 0, 4, 5, 7, 7, 1, 0, 19, 20, 1, 0, 21, 22, 1, 0, 35, 38, 1, 0, 24, 25, 1, 0, 26, 29, 1, 0, 30, 31, 253, 0, 38, 1, 0, 0, 0, 2, 44, 1, 0, 0, 0, 4, 53, 1, 0, 0, 0, 6, 58, 1, 0, 0, 0, 8, 60, 1, 0, 0, 0, 10, 76, 1, 0, 0, 0, 12, 84, 1, 0, 0, 0, 14, 87, 1, 0, 0, 0, 16, 139, 1, 0, 0, 0, 18, 164, 1, 0, 0, 0, 20, 189, 1, 0, 0, 0, 22, 197, 1, 0, 0, 0, 24, 201, 1, 0, 0, 0, 26, 212, 1, 0, 0, 0, 28, 215, 1, 0, 0, 0, 30, 225, 1, 0, 0, 0, 32, 229, 1, 0, 0, 0, 34, 35, 3, 2, 1, 0, 35, 36, 5, 1, 0, 0, 36, 39, 1, 0, 0, 0, 37, 39, 3, 8, 4, 0, 38, 34, 1, 0, 0, 0, 38, 37, 1, 0, 0, 0, 39, 40, 1, 0, 0, 0, 40, 38, 1, 0, 0, 0, 40, 41, 1, 0, 0, 0, 41, 42, 1, 0, 0, 0, 42, 43, 5, 0, 0, 1, 43, 1, 1, 0, 0, 0, 44, 45, 3, 6, 3, 0, 45, 50, 3, 4, 2, 0, 46, 47, 5, 2, 0, 0, 47, 49, 3, 4, 2, 0, 48, 46, 1, 0, 0, 0, 49, 52, 1, 0, 0, 0, 50, 48, 1, 0, 0, 0, 50, 51, 1, 0, 0, 0, 51, 3, 1, 0, 0, 0, 52, 50, 1, 0, 0, 0, 53, 56, 5, 38, 0, 0, 54, 55, 5, 3, 0, 0, 55, 57, 3, 18, 9, 0, 56, 54, 1, 0, 0, 0, 56, 57, 1, 0, 0, 0, 57, 5, 1, 0, 0, 0, 58, 59, 7, 0, 0, 0, 59, 7, 1, 0, 0, 0, 60, 61, 3, 6, 3, 0, 61, 62, 5, 38, 0, 0, 62, 64, 5, 8, 0, 0, 63, 65, 3, 10, 5, 0, 64, 63, 1, 0, 0, 0, 64, 65, 1, 0, 0, 0, 65, 66, 1, 0, 0, 0, 66, 67, 5, 9, 0, 0, 67, 71, 5, 10, 0, 0, 68, 70, 3, 16, 8, 0, 69, 68, 1, 0, 0, 0, 70, 73, 1, 0, 0, 0, 71, 69, 1, 0, 0, 0, 71, 72, 1, 0, 0, 0, 72, 74, 1, 0, 0, 0, 73, 71, 1, 0, 0, 0, 74, 75, 5, 11, 0, 0, 75, 9, 1, 0, 0, 0, 76, 81, 3, 12, 6, 0, 77, 78, 5, 2, 0, 0, 78, 80, 3, 12, 6, 0, 79, 77, 1, 0, 0, 0, 80, 83, 1, 0, 0, 0, 81, 79, 1, 0, 0, 0, 81, 82, 1, 0, 0, 0, 82, 11, 1, 0, 0, 0, 83, 81, 1, 0, 0, 0, 84, 85, 3, 6, 3, 0, 85, 86, 5, 38, 0, 0, 86, 13, 1, 0, 0, 0, 87, 91, 5, 10, 0, 0, 88, 90, 3, 16, 8, 0, 89, 88, 1, 0, 0, 0, 90, 93, 1, 0, 0, 0, 91, 89, 1, 0, 0, 0, 91, 92, 1, 0, 0, 0, 92, 94, 1, 0, 0, 0, 93, 91, 1, 0, 0, 0, 94, 95, 5, 11, 0, 0, 95, 15, 1, 0, 0, 0, 96, 140, 3, 14, 7, 0, 97, 98, 3, 2, 1, 0, 98, 99, 5, 1, 0, 0, 99, 140, 1, 0, 0, 0, 100, 101, 5, 12, 0, 0, 101, 102, 5, 8, 0, 0, 102, 103, 3, 18, 9, 0, 103, 104, 5, 9, 0, 0, 104, 107, 3, 16, 8, 0, 105, 106, 5, 13, 0, 0, 106, 108, 3, 16, 8, 0, 107, 105, 1, 0, 0, 0, 107, 108, 1, 0, 0, 0, 108, 140, 1, 0, 0, 0, 109, 140, 3, 24, 12, 0, 110, 111, 5, 14, 0, 0, 111, 112, 5, 8, 0, 0, 112, 113, 3, 18, 9, 0, 113, 114, 5, 9, 0, 0, 114, 115, 3, 16, 8, 0, 115, 140, 1, 0, 0, 0, 116, 117, 5, 15, 0, 0, 117, 118, 3, 16, 8, 0, 118, 119, 5, 14, 0, 0, 119, 120, 5, 8, 0, 0, 120, 121, 3, 18, 9, 0, 121, 122, 5, 9, 0, 0, 122, 123, 5, 1, 0, 0, 123, 140, 1, 0, 0, 0, 124, 126, 5, 16, 0, 0, 125, 127, 3, 18, 9, 0, 126, 125, 1, 0, 0, 0, 126, 127, 1, 0, 0, 0, 127, 128, 1, 0, 0, 0, 128, 140, 5, 1, 0, 0, 129, 130, 5, 17, 0, 0, 130, 140, 5, 1, 0, 0, 131, 132, 5, 18, 0, 0, 132, 140, 5, 1, 0, 0, 133, 134, 3, 22, 11, 0, 134, 135, 5, 1, 0, 0, 135, 140, 1, 0, 0, 0, 136, 137, 3, 18, 9, 0, 137, 138, 5, 1, 0, 0, 138, 140, 1, 0, 0, 0, 139, 96, 1, 0, 0, 0, 139, 97, 1, 0, 0, 0, 139, 100, 1, 0, 0, 0, 139, 109, 1, 0, 0, 0, 139, 110, 1, 0, 0, 0, 139, 116, 1, 0, 0, 0, 139, 124, 1, 0, 0, 0, 139, 129, 1, 0, 0, 0, 139, 131, 1, 0, 0, 0, 139, 133, 1, 0, 0, 0, 139, 136, 1, 0, 0, 0, 140, 17, 1, 0, 0, 0, 141, 142, 6, 9, -1, 0, 142, 143, 5, 38, 0, 0, 143, 145, 5, 8, 0, 0, 144, 146, 3, 20, 10, 0, 145, 144, 1, 0, 0, 0, 145, 146, 1, 0, 0, 0, 146, 147, 1, 0, 0, 0, 147, 165, 5, 9, 0, 0, 148, 149, 7, 1, 0, 0, 149, 150, 5, 8, 0, 0, 150, 151, 3, 18, 9, 0, 151, 152, 5, 9, 0, 0, 152, 165, 1, 0, 0, 0, 153, 154, 7, 2, 0, 0, 154, 165, 5, 38, 0, 0, 155, 156, 7, 3, 0, 0, 156, 165, 3, 18, 9, 10, 157, 158, 5, 23, 0, 0, 158, 165, 3, 18, 9, 9, 159, 165, 7, 4, 0, 0, 160, 161, 5, 8, 0, 0, 161, 162, 3, 18, 9, 0, 162, 163, 5, 9, 0, 0, 163, 165, 1, 0, 0, 0, 164, 141, 1, 0, 0, 0, 164, 148, 1, 0, 0, 0, 164, 153, 1, 0, 0, 0, 164, 155, 1, 0, 0, 0, 164, 157, 1, 0, 0, 0, 164, 159, 1, 0, 0, 0, 164, 160, 1, 0, 0, 0, 165, 186, 1, 0, 0, 0, 166, 167, 10, 8, 0, 0, 167, 168, 7, 5, 0, 0, 168, 185, 3, 18, 9, 9, 169, 170, 10, 7, 0, 0, 170, 171, 7, 3, 0, 0, 171, 185, 3, 18, 9, 8, 172, 173, 10, 6, 0, 0, 173, 174, 7, 6, 0, 0, 174, 185, 3, 18, 9, 7, 175, 176, 10, 5, 0, 0, 176, 177, 7, 7, 0, 0, 177, 185, 3, 18, 9, 6, 178, 179, 10, 4, 0, 0, 179, 180, 5, 32, 0, 0, 180, 185, 3, 18, 9, 5, 181, 182, 10, 3, 0, 0, 182, 183, 5, 33, 0, 0, 183, 185, 3, 18, 9, 4, 184, 166, 1, 0, 0, 0, 184, 169, 1, 0, 0, 0, 184, 172, 1, 0, 0, 0, 184, 175, 1, 0, 0, 0, 184, 178, 1, 0, 0, 0, 184, 181, 1, 0, 0, 0, 185, 188, 1, 0, 0, 0, 186, 184, 1, 0, 0, 0, 186, 187, 1, 0, 0, 0, 187, 19, 1, 0, 0, 0, 188, 186, 1, 0, 0, 0, 189, 194, 3, 18, 9, 0, 190, 191, 5, 2, 0, 0, 191, 193, 3, 18, 9, 0, 192, 190, 1, 0, 0, 0, 193, 196, 1, 0, 0, 0, 194, 192, 1, 0, 0, 0, 194, 195, 1, 0, 0, 0, 195, 21, 1, 0, 0, 0, 196, 194, 1, 0, 0, 0, 197, 198, 3, 18, 9, 0, 198, 199, 5, 3, 0, 0, 199, 200, 3, 18, 9, 0, 200, 23, 1, 0, 0, 0, 201, 202, 5, 34, 0, 0, 202, 203, 5, 8, 0, 0, 203, 204, 3, 26, 13, 0, 204, 205, 5, 1, 0, 0, 205, 206, 3, 28, 14, 0, 206, 207, 5, 1, 0, 0, 207, 208, 3, 30, 15, 0, 208, 209, 5, 9, 0, 0, 209, 210, 3, 16, 8, 0, 210, 25, 1, 0, 0, 0, 211, 213, 3, 2, 1, 0, 212, 211, 1, 0, 0, 0, 212, 213, 1, 0, 0, 0, 213, 27, 1, 0, 0, 0, 214, 216, 3, 18, 9, 0, 215, 214, 1, 0, 0, 0, 215, 216, 1, 0, 0, 0, 216, 29, 1, 0, 0, 0, 217, 222, 3, 32, 16, 0, 218, 219, 5, 2, 0, 0, 219, 221, 3, 32, 16, 0, 220, 218, 1, 0, 0, 0, 221, 224, 1, 0, 0, 0, 222, 220, 1, 0, 0, 0, 222, 223, 1, 0, 0, 0, 223, 226, 1, 0, 0, 0, 224, 222, 1, 0, 0, 0, 225, 217, 1, 0, 0, 0, 225, 226, 1, 0, 0, 0, 226, 31, 1, 0, 0, 0, 227, 230, 3, 18, 9, 0, 228, 230, 3, 22, 11, 0, 229, 227, 1, 0, 0, 0, 229, 228, 1, 0, 0, 0, 230, 33, 1, 0, 0, 0, 21, 38, 40, 50, 56, 64, 71, 81, 91, 107, 126, 139, 145, 164, 184, 186, 194, 212, 215, 222, 225, 229]