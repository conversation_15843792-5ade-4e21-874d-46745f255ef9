T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
T__18=19
T__19=20
T__20=21
T__21=22
T__22=23
T__23=24
T__24=25
T__25=26
T__26=27
T__27=28
T__28=29
T__29=30
T__30=31
T__31=32
T__32=33
T__33=34
BOOL=35
INT=36
FLOAT=37
ID=38
WS=39
LINECOMMENT=40
BLOCKCOMMENT=41
';'=1
','=2
'='=3
'float'=4
'int'=5
'void'=6
'bool'=7
'('=8
')'=9
'{'=10
'}'=11
'if'=12
'else'=13
'while'=14
'do'=15
'return'=16
'continue'=17
'break'=18
'++'=19
'--'=20
'-'=21
'+'=22
'!'=23
'*'=24
'/'=25
'<'=26
'<='=27
'>'=28
'>='=29
'=='=30
'!='=31
'&&'=32
'||'=33
'for'=34
