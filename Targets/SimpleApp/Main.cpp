//
//  Copyright © 2024-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
// External includes
#include <mpi.h>
// System includes
#include <iostream>


int main(int argc, char** argv)
{
    // Initialize the MPI environment.
    MPI_Init(&argc, &argv);

    // Get the number of processes.
    int worldSize = 0;
    int worldRank = 0;
    MPI_Comm_size(MPI_COMM_WORLD, &worldSize);
    MPI_Comm_rank(MPI_COMM_WORLD, &worldRank);

    // Get the name of the processor.
    char processorName[MPI_MAX_PROCESSOR_NAME];
    int nameLen = 0;
    MPI_Get_processor_name(processorName, &nameLen);
    std::string procName(processorName, nameLen);

    // Print off a hello world message.
    std::cout << std::format("Hello world from processor: {}, rank: {} out of {} processors\n",
                              procName, worldRank, worldSize) << std::endl;

    // Finalize the MPI environment.
    MPI_Finalize();

    return 0;
}
