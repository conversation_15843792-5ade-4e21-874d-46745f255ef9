#
#  Copyright © 2024-Present, Arkin Terl<PERSON>. All rights reserved.
#
#  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
#  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
#  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
#  trade secret or copyright law. Dissemination of this information or reproduction of this
#  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

set(TARGET_NAME SimpleApp)

add_executable(${TARGET_NAME}
        Main.cpp
)

add_dependencies(${TARGET_NAME} openmpi_cpp)

# Check if we're on macOS and add required frameworks.
if(APPLE)
    find_library(CORE_FOUNDATION_FRAMEWORK CoreFoundation REQUIRED)
    find_library(IOKIT_FRAMEWORK IOKit REQUIRED)
    find_package(ZLIB REQUIRED)
    target_link_libraries(${TARGET_NAME} PRIVATE
            ${CORE_FOUNDATION_FRAMEWORK} ${IOKIT_FRAMEWORK} ZLIB::ZLIB
            mpi event event_core event_extra event_pthreads pmix prrte open-pal hwloc)
else()
    target_link_libraries(${TARGET_NAME} PRIVATE
            mpi event event_core event_extra event_pthreads pmix prrte open-pal hwloc)
endif()

install(TARGETS ${TARGET_NAME}
        RUNTIME DESTINATION .
)
