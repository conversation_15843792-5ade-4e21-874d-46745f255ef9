name: CI

on:
  push:
    paths-ignore:
      - 'doc/**'
      - 'scripts/**'
      - 'LICENSE.txt'
      - 'README.md'
  pull_request:
    paths-ignore:
      - 'doc/**'
      - 'scripts/**'
      - 'LICENSE.txt'
      - 'README.md'

env:
  CTEST_OUTPUT_ON_FAILURE: ON
  CTEST_PARALLEL_LEVEL: 2

jobs:
  coverage:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Install
        run: sudo apt-get install -y ninja-build lcov

      - name: Generate
        run: cmake -B build -S . -G Ninja -D CMAKE_CXX_FLAGS="-fprofile-arcs -ftest-coverage"

      - name: Build
        run: cmake --build build

      - name: Test
        run: ctest --test-dir build --no-tests=error

      - name: LCOV
        run: |
          mkdir coverage
          lcov -c -d build/ -o coverage/lcov.info --include "*doctest/parts*"

      - name: Codecov
        uses: codecov/codecov-action@v2
        with:
          files: ./coverage/lcov.info
          fail_ci_if_error: true

  clang-tidy:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v2

      - name: Install
        run: sudo apt-get install -y ninja-build clang-tidy-14

      - name: Generate
        run: cmake -B build -S . -G Ninja -D CMAKE_CXX_COMPILER=clang++ -D CMAKE_EXPORT_COMPILE_COMMANDS=ON -D CMAKE_CXX_CLANG_TIDY="clang-tidy-14;-warnings-as-errors=*"

      - name: Build
        run: cmake --build build

  sanitizers:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ubuntu-22.04

    strategy:
      fail-fast: false
      matrix:
        sanitizers: ["address", "thread", "undefined", "integer", "implicit-conversion", "nullability", "safe-stack"]

    steps:
      - uses: actions/checkout@v2

      - name: Install
        run: sudo apt-get install -y ninja-build

      - name: Generate
        run: cmake -B build -S . -G Ninja -D CMAKE_CXX_COMPILER=clang++ -D CMAKE_EXPORT_COMPILE_COMMANDS=ON -D CMAKE_CXX_FLAGS="-fsanitize=${{ matrix.sanitizers }}"

      - name: Build
        run: cmake --build build

      - name: Test
        run: ctest --test-dir build --no-tests=error

  ci:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ${{ matrix.os }}

    env:
      CMAKE_GENERATOR: Ninja
      ASAN_OPTIONS: strict_string_checks=true:detect_odr_violation=2:detect_stack_use_after_return=true:check_initialization_order=true:strict_init_order=true
      TSAN_OPTIONS: force_seq_cst_atomics=1

    strategy:
      fail-fast: false
      matrix:
        os: ["windows-2019", "windows-2022"]
        compiler: ["cl", "clang", "clang-cl"]

        include:
          - os: ubuntu-18.04
            compiler: gcc
            version: "4.8"

          - os: ubuntu-18.04
            compiler: gcc
            version: "4.9"

          - os: ubuntu-18.04
            compiler: gcc
            version: "5"

          - os: ubuntu-18.04
            compiler: gcc
            version: "6"

          - os: ubuntu-18.04
            compiler: gcc
            version: "7"

          - os: ubuntu-18.04
            compiler: gcc
            version: "8"

          - os: ubuntu-latest
            compiler: gcc
            version: "9"

          - os: ubuntu-latest
            compiler: gcc
            version: "10"

          - os: ubuntu-latest
            compiler: gcc
            version: "11"

          - os: ubuntu-latest
            compiler: gcc
            version: "12"

          - os: ubuntu-18.04
            compiler: clang
            version: "3.5"

          - os: ubuntu-18.04
            compiler: clang
            version: "3.6"

          - os: ubuntu-18.04
            compiler: clang
            version: "3.7"

          - os: ubuntu-18.04
            compiler: clang
            version: "3.8"

          - os: ubuntu-18.04
            compiler: clang
            version: "3.9"

          - os: ubuntu-18.04
            compiler: clang
            version: "4.0"

          - os: ubuntu-18.04
            compiler: clang
            version: "5.0"

          - os: ubuntu-18.04
            compiler: clang
            version: "6.0"

          - os: ubuntu-18.04
            compiler: clang
            version: "7"

          - os: ubuntu-18.04
            compiler: clang
            version: "8"

          - os: ubuntu-20.04
            compiler: clang
            version: "9"

          - os: ubuntu-20.04
            compiler: clang
            version: "10"

          - os: ubuntu-20.04
            compiler: clang
            version: "11"

          - os: ubuntu-20.04
            compiler: clang
            version: "12"

          - os: ubuntu-latest
            compiler: clang
            version: "13"

          - os: ubuntu-latest
            compiler: clang
            version: "14"

          - os: ubuntu-latest
            compiler: clang
            version: "15"

          - os: macOS-10.15
            compiler: xcode
            version: "10.3"

          - os: macOS-latest
            compiler: xcode
            version: "13.2.1"

          - os: macos-12
            compiler: xcode
            version: "14.0.1"

          - os: macOS-latest
            compiler: gcc
            version: "11"

    steps:
      - uses: actions/checkout@v2

      - name: Install (Linux)
        if: runner.os == 'Linux'
        run: |
          # Required for libc6-dbg:i386 and g++-multilib packages which are
          # needed for x86 builds.
          sudo dpkg --add-architecture i386

          sudo apt-get update

          # libc6-dbg:i386 is required for running valgrind on x86.
          sudo apt-get install -y ninja-build valgrind libc6-dbg:i386 wget gpg ca-certificates

          # clang-3.7 and earlier are not available in Bionic anymore so we get
          # them from the Xenial repositories instead.
          sudo gpg --keyserver keyserver.ubuntu.com --recv-keys 40976EAF437D05B5
          sudo gpg --keyserver keyserver.ubuntu.com --recv-keys 3B4FE6ACC0B21F32
          #sudo sh -c 'wget -q -O - "https://keyserver.ubuntu.com/pks/lookup?search=0x40976EAF437D05B5&fingerprint=on&op=get" | gpg -q --dearmor - | tee "/usr/share/keyrings/dk.gpg" > /dev/null'
          sudo add-apt-repository "deb http://dk.archive.ubuntu.com/ubuntu/ xenial main"
          sudo add-apt-repository "deb http://dk.archive.ubuntu.com/ubuntu/ xenial universe"

          # clang->=13 is not currently available by default
          # causing compiler conflict for clang-14 as the below pulls from focal for 22.04 (ubuntu-latest)
          #if [ "${{ matrix.compiler }}" = "clang" -a ${{ matrix.version }} -ge 13  ]; then
            #wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
            #sudo add-apt-repository "deb https://apt.llvm.org/focal/ llvm-toolchain-focal-${{ matrix.version }} main"
          #fi

          if [ "${{ matrix.compiler }}" = "gcc" ]; then
            sudo apt-get install -y g++-${{ matrix.version }} g++-${{ matrix.version }}-multilib
          else
            sudo apt-get install -y clang-${{ matrix.version }} libclang-${{ matrix.version }}-dev llvm-${{ matrix.version }}-dev g++-multilib
          fi

      - name: Install (macOS)
        if: runner.os == 'macOS'
        run: |
            brew install ninja
            if [ "${{ matrix.compiler }}" = "xcode" ]; then
              sudo xcode-select -switch /Applications/Xcode_${{ matrix.version }}.app
            fi

      - name: Configure x64
        uses: ilammy/msvc-dev-cmd@v1

      - name: Build & Test x64
        run: python3 .github/workflows/build_and_test.py ${{ runner.os }} x64 ${{ matrix.compiler }} ${{ matrix.version }}

      - name: Configure x86
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x86

      # MacOS doesn't support x86 from Xcode 10 onwards.

      - name: Build & Test x86
        if: runner.os == 'Linux' || runner.os == 'Windows' && matrix.compiler != 'clang-cl'
        run: python3 .github/workflows/build_and_test.py ${{ runner.os }} x86 ${{ matrix.compiler }} ${{ matrix.version }}

  ci-min-gw:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: windows-latest

    strategy:
      fail-fast: false
      matrix:
        configuration: ["Debug", "Release"]

    steps:
      - uses: actions/checkout@v2

      - name: Set up MinGW
        uses: egor-tensin/setup-mingw@v2

      - name: Generate
        run: cmake -B build -S . -G "MinGW Makefiles" -D CMAKE_BUILD_TYPE=${{ matrix.configuration }}

      - name: Build
        run: cmake --build build

      - name: Test
        run: ctest --test-dir build --no-tests=error

  ci-msvs:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ${{ matrix.toolset == 'v143' && 'windows-2022' || 'windows-2019' }}

    strategy:
      fail-fast: false
      matrix:
        toolset: ["v140", "v141", "v142", "v143", "ClangCl"]
        architecture: ["Win32", "x64"]
        configuration: ["Debug", "Release"]

    steps:
      - uses: actions/checkout@v2

      - name: Generate
        run: cmake -B build -S . -G "${{ matrix.toolset == 'v143' && 'Visual Studio 17 2022' || 'Visual Studio 16 2019' }}" \
            -A ${{ matrix.architecture }} -T ${{ matrix.toolset }}

      - name: Build
        run: cmake --build build --config ${{ matrix.configuration }}

      - name: Test
        run: ctest -C ${{ matrix.configuration }} --test-dir build --no-tests=error

  ci-icpc:
    if: github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        configuration: ["Debug", "Release"]

    steps:
      - uses: actions/checkout@v2

      - name: Install
        run: |
          #wget -O - https://apt.repos.intel.com/intel-gpg-keys/GPG-PUB-KEY-INTEL-SW-PRODUCTS.PUB | sudo apt-key add -
          #sudo add-apt-repository "deb [signed-by=/etc/apt/keyrings/icpc.gpg] https://apt.repos.intel.com/oneapi all main"
          # apt-key is deprecated replace with below lines
          wget -O - https://apt.repos.intel.com/intel-gpg-keys/GPG-PUB-KEY-INTEL-SW-PRODUCTS.PUB | sudo gpg --dearmor |sudo tee /etc/apt/keyrings/icpc.gpg > /dev/null
          echo "deb [signed-by=/etc/apt/keyrings/icpc.gpg] https://apt.repos.intel.com/oneapi all main" | sudo tee /etc/apt/sources.list.d/icpc.list
          sudo apt-get update
          sudo apt-get install -y intel-oneapi-compiler-dpcpp-cpp-and-cpp-classic ninja-build

      - name: Generate
        run: |
          . /opt/intel/oneapi/setvars.sh
          cmake -B build -S . -G Ninja -D CMAKE_CXX_COMPILER=icc -D CMAKE_C_COMPILER=icc -D CMAKE_CXX_COMPILER=icpc -D CMAKE_BUILD_TYPE=${{ matrix.configuration }} -D CMAKE_CXX_FLAGS="-w2 -Wcheck -Werror -diag-disable=10441 -D DOCTEST_CONFIG_NO_CONTRADICTING_INLINE"

      - name: Build
        run: |
          . /opt/intel/oneapi/setvars.sh
          cmake --build build --config ${{ matrix.configuration }}
      - name: Test
        run: |
          . /opt/intel/oneapi/setvars.sh
          ctest -C ${{ matrix.configuration }} --test-dir build --no-tests=error

