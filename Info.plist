<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>stt</string>
    <key>CFBundleIdentifier</key>
    <string>com.example.stt</string>
    <key>CFBundleName</key>
    <string>Speech to Text App</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires microphone access to perform speech-to-text.</string>
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>This app requires speech recognition access to perform speech-to-text.</string>
</dict>
</plist>
