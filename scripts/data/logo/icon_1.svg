<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW X7 -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="20.2621mm" height="26.4073mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 373 486"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
   <![CDATA[
    .fil0 {fill:#FEFEFE}
    .fil4 {fill:#39356C}
    .fil1 {fill:#39356C}
    .fil3 {fill:#5EE045}
    .fil2 {fill:#5EE045}
   ]]>
  </style>
 </defs>
 <g id="Capa_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <path class="fil0" d="M280 89c27,-2 53,1 79,5 21,3 17,35 -7,30 -33,-6 -67,-7 -99,3 -32,10 -44,36 -43,68 2,28 -4,53 -18,77 -11,19 -26,35 -41,50 -42,39 -46,90 -37,143 4,23 -25,28 -32,9 -47,-131 -35,-266 49,-380 14,-20 33,-34 57,-38 34,-6 67,11 92,33z"/>
  <path class="fil1" d="M276 100c27,-2 54,0 81,4 8,2 7,11 -3,9 -34,-6 -70,-7 -104,4 -36,11 -52,41 -50,79 2,52 -23,87 -57,118 -44,41 -49,95 -39,153 1,10 -9,10 -12,3 -44,-121 -38,-254 47,-370 36,-49 92,-43 137,0z"/>
  <path class="fil0" d="M208 262c-16,74 -106,100 -170,115 -21,5 -30,-24 -10,-31 25,-8 61,-17 79,-36 7,-7 11,-15 14,-24 7,-24 -7,-43 -27,-57 -77,-52 -105,-124 -90,-215 3,-18 27,-19 33,-2 14,42 50,63 88,80 74,33 100,91 83,170z"/>
  <path class="fil2" d="M198 259c-14,64 -84,90 -163,108 -7,2 -12,-8 -4,-11 44,-14 87,-25 99,-67 8,-28 -5,-51 -30,-68 -79,-54 -99,-126 -86,-205 1,-7 10,-8 13,0 16,45 53,67 94,85 68,30 94,83 77,158z"/>
  <path class="fil3" d="M167 362c18,-3 28,-12 30,-30 1,-18 31,-17 31,0 0,22 13,29 30,30 19,1 18,30 0,31 -19,1 -28,12 -30,30 -2,19 -31,19 -31,0 0,-19 -10,-31 -30,-30 -16,1 -18,-28 0,-31z"/>
  <path class="fil4" d="M257 307c18,-3 28,-13 30,-30 1,-18 30,-18 31,0 0,22 12,29 29,30 20,0 19,30 0,31 -19,1 -28,12 -29,30 -2,19 -31,19 -31,0 0,-19 -10,-31 -30,-30 -16,1 -18,-29 0,-31z"/>
 </g>
</svg>
